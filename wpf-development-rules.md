# WPF开发AI辅助规则文档

## 概述
本文档为AI助手提供WPF（Windows Presentation Foundation）开发的专业指导规则，确保生成高质量、符合最佳实践的WPF应用程序代码。

### 推荐技术栈
本文档基于以下现代WPF开发技术栈：
- **Microsoft.Extensions.DependencyInjection** - 依赖注入容器
- **CommunityToolkit.Mvvm** - 现代MVVM框架
- **Syncfusion WPF控件库** - 企业级UI控件
- **Entity Framework Core** - 数据访问层
- **.NET 6/7/8** - 现代.NET平台

## 1. WPF项目结构规范

### 1.1 推荐文件夹结构
```
ProjectName/
├── Views/                  # 视图文件（.xaml）
│   ├── MainWindow.xaml
│   ├── UserControls/      # 用户控件
│   ├── Pages/             # 页面控件
│   └── Dialogs/           # 对话框
├── ViewModels/            # 视图模型
│   ├── MainViewModel.cs
│   ├── Base/              # 基础ViewModel类
│   └── Dialogs/           # 对话框ViewModels
├── Models/                # 数据模型
│   ├── Entities/          # 实体类
│   ├── DTOs/              # 数据传输对象
│   └── Enums/             # 枚举类型
├── Services/              # 业务服务
│   ├── Interfaces/        # 服务接口
│   ├── Implementations/   # 服务实现
│   └── Configuration/     # 服务配置
├── Data/                  # 数据访问层
│   ├── Context/           # DbContext
│   ├── Repositories/      # 仓储模式
│   └── Migrations/        # EF迁移文件
├── Resources/             # 资源文件
│   ├── Styles/            # 样式文件
│   ├── Templates/         # 模板文件
│   ├── Images/            # 图片资源
│   ├── Dictionaries/      # 资源字典
│   └── Syncfusion/        # Syncfusion主题资源
├── Converters/            # 值转换器
├── Behaviors/             # 行为类
├── Controls/              # 自定义控件
├── Utilities/             # 工具类
├── Extensions/            # 扩展方法
└── Configuration/         # 应用配置
    ├── DependencyInjection/ # DI配置
    └── Settings/          # 应用设置
```

### 1.2 命名约定
- **Views**: 使用PascalCase，以功能描述命名（如`CustomerListView.xaml`）
- **ViewModels**: 对应View名称 + "ViewModel"（如`CustomerListViewModel.cs`）
- **Models**: 使用PascalCase，描述性名称（如`Customer.cs`）
- **Services**: 接口以"I"开头，实现类以"Service"结尾（如`ICustomerService.cs`, `CustomerService.cs`）

### 1.3 代码组织原则
- 每个View对应一个ViewModel
- 复杂的业务逻辑放在Service层
- 使用依赖注入管理对象生命周期
- 资源文件按功能模块组织

## 2. MVVM模式实施指南

### 2.1 ViewModel设计原则
```csharp
// 基础ViewModel示例
public abstract class ViewModelBase : INotifyPropertyChanged
{
    public event PropertyChangedEventHandler PropertyChanged;
    
    protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }
    
    protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string propertyName = null)
    {
        if (EqualityComparer<T>.Default.Equals(field, value)) return false;
        field = value;
        OnPropertyChanged(propertyName);
        return true;
    }
}
```

### 2.2 数据绑定最佳实践
- 使用`INotifyPropertyChanged`接口实现属性变更通知
- 优先使用双向绑定（`{Binding Property, Mode=TwoWay}`）
- 对于集合使用`ObservableCollection<T>`
- 使用`ICollectionView`进行数据过滤和排序

### 2.3 Command模式规范
```csharp
// 使用CommunityToolkit.Mvvm的RelayCommand
[RelayCommand]
private async Task SaveCustomer()
{
    // 保存逻辑
}

// 带参数的Command
[RelayCommand]
private void DeleteCustomer(Customer customer)
{
    // 删除逻辑
}
```

### 2.4 依赖注入集成
- 推荐使用Microsoft.Extensions.DependencyInjection
- 在App.xaml.cs中配置服务容器
- ViewModel通过构造函数注入依赖服务

## 3. XAML编写规范

### 3.1 控件布局标准
```xml
<!-- 使用Grid进行复杂布局 -->
<Grid>
    <Grid.RowDefinitions>
        <RowDefinition Height="Auto"/>
        <RowDefinition Height="*"/>
        <RowDefinition Height="Auto"/>
    </Grid.RowDefinitions>
    
    <!-- 标题栏 -->
    <TextBlock Grid.Row="0" 
               Text="{Binding Title}" 
               Style="{StaticResource HeaderTextStyle}"/>
    
    <!-- 内容区域 -->
    <ScrollViewer Grid.Row="1">
        <!-- 内容 -->
    </ScrollViewer>
    
    <!-- 按钮栏 -->
    <StackPanel Grid.Row="2" 
                Orientation="Horizontal" 
                HorizontalAlignment="Right">
        <Button Content="保存" Command="{Binding SaveCommand}"/>
        <Button Content="取消" Command="{Binding CancelCommand}"/>
    </StackPanel>
</Grid>
```

### 3.2 样式定义规范
```xml
<!-- 在ResourceDictionary中定义样式 -->
<Style x:Key="HeaderTextStyle" TargetType="TextBlock">
    <Setter Property="FontSize" Value="18"/>
    <Setter Property="FontWeight" Value="Bold"/>
    <Setter Property="Margin" Value="0,0,0,10"/>
    <Setter Property="Foreground" Value="{DynamicResource PrimaryBrush}"/>
</Style>
```

### 3.3 数据模板设计
```xml
<DataTemplate x:Key="CustomerItemTemplate" DataType="{x:Type models:Customer}">
    <Border BorderBrush="Gray" BorderThickness="1" Margin="2" Padding="5">
        <StackPanel>
            <TextBlock Text="{Binding Name}" FontWeight="Bold"/>
            <TextBlock Text="{Binding Email}" Foreground="Gray"/>
        </StackPanel>
    </Border>
</DataTemplate>
```

## 4. 性能优化指导

### 4.1 数据虚拟化
- 对于大数据集使用`VirtualizingStackPanel`
- 启用容器回收：`VirtualizingPanel.VirtualizationMode="Recycling"`
- 使用`ListView`或`DataGrid`的内置虚拟化功能

### 4.2 内存泄漏预防
- 及时取消事件订阅
- 使用弱引用处理长生命周期对象
- 正确处理`IDisposable`对象
- 避免在ViewModel中直接引用View

### 4.3 渲染性能优化
- 减少复杂的视觉效果和动画
- 使用`BitmapCache`缓存复杂控件
- 避免频繁的布局更新
- 使用`Freezable`对象提高性能

## 5. Microsoft.Extensions.DependencyInjection集成

### 5.1 应用程序配置和初始化

#### 5.1.1 App.xaml.cs完整配置
```csharp
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Windows;
using System.IO;

public partial class App : Application
{
    private IHost _host;

    protected override async void OnStartup(StartupEventArgs e)
    {
        // 创建Host Builder
        _host = Host.CreateDefaultBuilder(e.Args)
            .ConfigureAppConfiguration((context, config) =>
            {
                config.SetBasePath(Directory.GetCurrentDirectory());
                config.AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);
                config.AddJsonFile($"appsettings.{context.HostingEnvironment.EnvironmentName}.json",
                    optional: true, reloadOnChange: true);
                config.AddEnvironmentVariables();
                config.AddCommandLine(e.Args);
            })
            .ConfigureServices((context, services) =>
            {
                ConfigureServices(services, context.Configuration);
            })
            .ConfigureLogging((context, logging) =>
            {
                logging.ClearProviders();
                logging.AddConsole();
                logging.AddDebug();
                logging.AddEventLog();
            })
            .UseContentRoot(Directory.GetCurrentDirectory())
            .Build();

        // 启动Host
        await _host.StartAsync();

        // 创建并显示主窗口
        var mainWindow = _host.Services.GetRequiredService<MainWindow>();
        mainWindow.Show();

        base.OnStartup(e);
    }

    private void ConfigureServices(IServiceCollection services, IConfiguration configuration)
    {
        // 注册配置
        services.Configure<AppSettings>(configuration.GetSection("AppSettings"));
        services.Configure<DatabaseSettings>(configuration.GetSection("Database"));

        // 注册DbContext
        services.AddDbContext<ApplicationDbContext>(options =>
        {
            var connectionString = configuration.GetConnectionString("DefaultConnection");
            options.UseSqlServer(connectionString);
        });

        // 注册服务 - Singleton（应用程序生命周期）
        services.AddSingleton<ILoggingService, LoggingService>();
        services.AddSingleton<IConfigurationService, ConfigurationService>();
        services.AddSingleton<IEventAggregator, EventAggregator>();

        // 注册服务 - Scoped（请求生命周期，在WPF中通常等同于Transient）
        services.AddScoped<ICustomerService, CustomerService>();
        services.AddScoped<IOrderService, OrderService>();
        services.AddScoped<IReportService, ReportService>();

        // 注册服务 - Transient（每次请求创建新实例）
        services.AddTransient<IDialogService, DialogService>();
        services.AddTransient<INavigationService, NavigationService>();
        services.AddTransient<IValidationService, ValidationService>();

        // 注册ViewModels - Transient（每次创建新实例）
        services.AddTransient<MainViewModel>();
        services.AddTransient<CustomerListViewModel>();
        services.AddTransient<CustomerDetailViewModel>();
        services.AddTransient<OrderListViewModel>();
        services.AddTransient<ReportViewModel>();

        // 注册Views - Transient
        services.AddTransient<MainWindow>();
        services.AddTransient<CustomerListView>();
        services.AddTransient<CustomerDetailView>();
        services.AddTransient<OrderListView>();

        // 注册工厂模式服务
        services.AddTransient<Func<Type, object>>(serviceProvider =>
            viewModelType => serviceProvider.GetRequiredService(viewModelType));

        // 注册Syncfusion许可证
        RegisterSyncfusionLicense();
    }

    private void RegisterSyncfusionLicense()
    {
        // 在实际项目中，从配置文件或环境变量读取许可证密钥
        var licenseKey = "YOUR_SYNCFUSION_LICENSE_KEY";
        Syncfusion.Licensing.SyncfusionLicenseProvider.RegisterLicense(licenseKey);
    }

    protected override async void OnExit(ExitEventArgs e)
    {
        if (_host != null)
        {
            await _host.StopAsync();
            _host.Dispose();
        }
        base.OnExit(e);
    }
}
```

#### 5.1.2 配置模型定义
```csharp
// Configuration/Settings/AppSettings.cs
public class AppSettings
{
    public string ApplicationName { get; set; } = "WPF Application";
    public string Version { get; set; } = "1.0.0";
    public bool EnableLogging { get; set; } = true;
    public int MaxRetryAttempts { get; set; } = 3;
    public TimeSpan RequestTimeout { get; set; } = TimeSpan.FromSeconds(30);
}

// Configuration/Settings/DatabaseSettings.cs
public class DatabaseSettings
{
    public string ConnectionString { get; set; }
    public int CommandTimeout { get; set; } = 30;
    public bool EnableSensitiveDataLogging { get; set; } = false;
    public bool EnableDetailedErrors { get; set; } = false;
}
```

### 5.2 服务生命周期管理最佳实践

#### 5.2.1 生命周期选择指南
```csharp
// Singleton - 应用程序生命周期，全局共享状态
// 适用于：配置服务、日志服务、缓存服务、事件聚合器
services.AddSingleton<ILoggingService, LoggingService>();
services.AddSingleton<ICacheService, CacheService>();
services.AddSingleton<IEventAggregator, EventAggregator>();

// Scoped - 在WPF中通常等同于Transient，但在某些场景下有用
// 适用于：数据库上下文、工作单元模式
services.AddScoped<ApplicationDbContext>();
services.AddScoped<IUnitOfWork, UnitOfWork>();

// Transient - 每次请求创建新实例
// 适用于：ViewModels、轻量级服务、无状态服务
services.AddTransient<CustomerViewModel>();
services.AddTransient<IValidationService, ValidationService>();
```

#### 5.2.2 资源释放和生命周期管理
```csharp
// Services/Base/DisposableService.cs
public abstract class DisposableService : IDisposable
{
    private bool _disposed = false;

    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed)
        {
            if (disposing)
            {
                // 释放托管资源
                DisposeManagedResources();
            }

            // 释放非托管资源
            DisposeUnmanagedResources();
            _disposed = true;
        }
    }

    protected virtual void DisposeManagedResources() { }
    protected virtual void DisposeUnmanagedResources() { }

    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    ~DisposableService()
    {
        Dispose(false);
    }
}

// 实现示例
public class CustomerService : DisposableService, ICustomerService
{
    private readonly ApplicationDbContext _context;
    private readonly ILogger<CustomerService> _logger;

    public CustomerService(ApplicationDbContext context, ILogger<CustomerService> logger)
    {
        _context = context;
        _logger = logger;
    }

    protected override void DisposeManagedResources()
    {
        _context?.Dispose();
        base.DisposeManagedResources();
    }

    // 服务方法实现...
}
```

## 6. CommunityToolkit.Mvvm深度集成

### 6.1 基类选择和使用指导

#### 6.1.1 ObservableObject vs ObservableRecipient
```csharp
// 使用ObservableObject - 适用于简单的ViewModel
public partial class SimpleViewModel : ObservableObject
{
    [ObservableProperty]
    private string _title;

    [ObservableProperty]
    private bool _isLoading;
}

// 使用ObservableRecipient - 适用于需要消息传递的ViewModel
public partial class ComplexViewModel : ObservableRecipient
{
    public ComplexViewModel(IMessenger messenger) : base(messenger)
    {
        // 激活消息接收
        IsActive = true;
    }

    [ObservableProperty]
    private Customer _selectedCustomer;

    // 接收消息
    [RelayCommand]
    private void OnReceive(CustomerUpdatedMessage message)
    {
        if (message.CustomerId == SelectedCustomer?.Id)
        {
            SelectedCustomer = message.UpdatedCustomer;
        }
    }
}
```

#### 6.1.2 源生成器特性使用规范
```csharp
public partial class CustomerViewModel : ObservableObject
{
    private readonly ICustomerService _customerService;
    private readonly IMessenger _messenger;

    public CustomerViewModel(ICustomerService customerService, IMessenger messenger)
    {
        _customerService = customerService;
        _messenger = messenger;
        Customers = new ObservableCollection<Customer>();
    }

    // [ObservableProperty] - 自动生成属性和PropertyChanged通知
    [ObservableProperty]
    private string _searchText;

    [ObservableProperty]
    private Customer _selectedCustomer;

    [ObservableProperty]
    private bool _isLoading;

    [ObservableProperty]
    [NotifyPropertyChangedFor(nameof(HasCustomers))]
    [NotifyCanExecuteChangedFor(nameof(DeleteSelectedCommand))]
    private ObservableCollection<Customer> _customers;

    // 计算属性
    public bool HasCustomers => Customers?.Count > 0;

    // [RelayCommand] - 自动生成Command和CanExecute方法
    [RelayCommand]
    private async Task LoadCustomers()
    {
        IsLoading = true;
        try
        {
            var customers = await _customerService.GetCustomersAsync();
            Customers.Clear();
            foreach (var customer in customers)
            {
                Customers.Add(customer);
            }
        }
        catch (Exception ex)
        {
            // 错误处理
            _messenger.Send(new ErrorMessage(ex.Message));
        }
        finally
        {
            IsLoading = false;
        }
    }

    // 带参数的Command
    [RelayCommand]
    private async Task EditCustomer(Customer customer)
    {
        if (customer != null)
        {
            _messenger.Send(new NavigateToCustomerDetailMessage(customer.Id));
        }
    }

    // 带CanExecute的Command
    [RelayCommand(CanExecute = nameof(CanDeleteSelected))]
    private async Task DeleteSelected()
    {
        if (SelectedCustomer != null)
        {
            await _customerService.DeleteCustomerAsync(SelectedCustomer.Id);
            Customers.Remove(SelectedCustomer);
            _messenger.Send(new CustomerDeletedMessage(SelectedCustomer.Id));
        }
    }

    private bool CanDeleteSelected() => SelectedCustomer != null;

    // 异步Command with CancellationToken
    [RelayCommand]
    private async Task SearchCustomers(CancellationToken cancellationToken)
    {
        if (string.IsNullOrWhiteSpace(SearchText))
        {
            await LoadCustomersCommand.ExecuteAsync(cancellationToken);
            return;
        }

        IsLoading = true;
        try
        {
            var customers = await _customerService.SearchCustomersAsync(SearchText, cancellationToken);
            Customers.Clear();
            foreach (var customer in customers)
            {
                Customers.Add(customer);
            }
        }
        catch (OperationCanceledException)
        {
            // 操作被取消
        }
        finally
        {
            IsLoading = false;
        }
    }
}
```

### 6.2 Messenger模式实现和最佳实践

#### 6.2.1 消息定义
```csharp
// Messages/CustomerMessages.cs
public record CustomerUpdatedMessage(int CustomerId, Customer UpdatedCustomer);
public record CustomerDeletedMessage(int CustomerId);
public record CustomerCreatedMessage(Customer NewCustomer);
public record NavigateToCustomerDetailMessage(int CustomerId);

// Messages/ApplicationMessages.cs
public record ErrorMessage(string Message, Exception Exception = null);
public record SuccessMessage(string Message);
public record LoadingStateChangedMessage(bool IsLoading);
public record NavigationMessage(string ViewName, object Parameter = null);
```

#### 6.2.2 Messenger服务注册
```csharp
// 在App.xaml.cs的ConfigureServices方法中
private void ConfigureServices(IServiceCollection services, IConfiguration configuration)
{
    // 注册Messenger - Singleton确保全局唯一实例
    services.AddSingleton<IMessenger, WeakReferenceMessenger>();

    // 或者使用StrongReferenceMessenger（需要手动管理订阅）
    // services.AddSingleton<IMessenger, StrongReferenceMessenger>();

    // 其他服务注册...
}
```

#### 6.2.3 消息发送和接收模式
```csharp
// 发送消息的ViewModel
public partial class CustomerEditViewModel : ObservableObject
{
    private readonly ICustomerService _customerService;
    private readonly IMessenger _messenger;

    public CustomerEditViewModel(ICustomerService customerService, IMessenger messenger)
    {
        _customerService = customerService;
        _messenger = messenger;
    }

    [RelayCommand]
    private async Task SaveCustomer()
    {
        try
        {
            await _customerService.SaveCustomerAsync(Customer);

            // 发送成功消息
            _messenger.Send(new CustomerUpdatedMessage(Customer.Id, Customer));
            _messenger.Send(new SuccessMessage("客户信息保存成功"));

            // 导航回列表页面
            _messenger.Send(new NavigationMessage("CustomerList"));
        }
        catch (Exception ex)
        {
            _messenger.Send(new ErrorMessage("保存客户信息失败", ex));
        }
    }
}

// 接收消息的ViewModel
public partial class CustomerListViewModel : ObservableRecipient,
    IRecipient<CustomerUpdatedMessage>,
    IRecipient<CustomerDeletedMessage>,
    IRecipient<CustomerCreatedMessage>
{
    public CustomerListViewModel(IMessenger messenger) : base(messenger)
    {
        IsActive = true; // 激活消息接收
    }

    public void Receive(CustomerUpdatedMessage message)
    {
        var existingCustomer = Customers.FirstOrDefault(c => c.Id == message.CustomerId);
        if (existingCustomer != null)
        {
            var index = Customers.IndexOf(existingCustomer);
            Customers[index] = message.UpdatedCustomer;
        }
    }

    public void Receive(CustomerDeletedMessage message)
    {
        var customerToRemove = Customers.FirstOrDefault(c => c.Id == message.CustomerId);
        if (customerToRemove != null)
        {
            Customers.Remove(customerToRemove);
        }
    }

    public void Receive(CustomerCreatedMessage message)
    {
        Customers.Add(message.NewCustomer);
    }
}
```

### 6.3 高级特性和性能优化

#### 6.3.1 属性验证集成
```csharp
public partial class CustomerViewModel : ObservableValidator
{
    [ObservableProperty]
    [Required(ErrorMessage = "客户姓名不能为空")]
    [MinLength(2, ErrorMessage = "客户姓名至少需要2个字符")]
    private string _name;

    [ObservableProperty]
    [Required(ErrorMessage = "邮箱地址不能为空")]
    [EmailAddress(ErrorMessage = "请输入有效的邮箱地址")]
    private string _email;

    [ObservableProperty]
    [Range(18, 120, ErrorMessage = "年龄必须在18-120之间")]
    private int _age;

    // 自定义验证
    [RelayCommand(CanExecute = nameof(CanSave))]
    private async Task Save()
    {
        ValidateAllProperties();

        if (!HasErrors)
        {
            await _customerService.SaveCustomerAsync(new Customer
            {
                Name = Name,
                Email = Email,
                Age = Age
            });
        }
    }

    private bool CanSave() => !HasErrors && !string.IsNullOrWhiteSpace(Name);
}
```

### 6.4 与依赖注入的集成模式

#### 6.4.1 ViewModel工厂模式
```csharp
// Services/Interfaces/IViewModelFactory.cs
public interface IViewModelFactory
{
    T CreateViewModel<T>() where T : class;
    object CreateViewModel(Type viewModelType);
}

// Services/Implementations/ViewModelFactory.cs
public class ViewModelFactory : IViewModelFactory
{
    private readonly IServiceProvider _serviceProvider;

    public ViewModelFactory(IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider;
    }

    public T CreateViewModel<T>() where T : class
    {
        return _serviceProvider.GetRequiredService<T>();
    }

    public object CreateViewModel(Type viewModelType)
    {
        return _serviceProvider.GetRequiredService(viewModelType);
    }
}

// 在DI容器中注册
services.AddSingleton<IViewModelFactory, ViewModelFactory>();
```

## 7. Syncfusion WPF控件库集成

### 7.1 许可证管理和初始化

#### 7.1.1 许可证配置
```csharp
// App.xaml.cs中的许可证注册
public partial class App : Application
{
    protected override void OnStartup(StartupEventArgs e)
    {
        // 注册Syncfusion许可证（在任何Syncfusion控件使用之前）
        RegisterSyncfusionLicense();

        base.OnStartup(e);
    }

    private void RegisterSyncfusionLicense()
    {
        // 方法1：从配置文件读取
        var configuration = _host.Services.GetRequiredService<IConfiguration>();
        var licenseKey = configuration["Syncfusion:LicenseKey"];

        // 方法2：从环境变量读取
        // var licenseKey = Environment.GetEnvironmentVariable("SYNCFUSION_LICENSE_KEY");

        // 方法3：从嵌入资源读取（生产环境推荐）
        // var licenseKey = GetEmbeddedLicenseKey();

        if (!string.IsNullOrEmpty(licenseKey))
        {
            Syncfusion.Licensing.SyncfusionLicenseProvider.RegisterLicense(licenseKey);
        }
        else
        {
            // 开发环境警告
            #if DEBUG
            MessageBox.Show("Syncfusion许可证未配置，某些功能可能受限", "许可证警告",
                MessageBoxButton.OK, MessageBoxImage.Warning);
            #endif
        }
    }

    private string GetEmbeddedLicenseKey()
    {
        var assembly = Assembly.GetExecutingAssembly();
        var resourceName = "YourApp.Resources.SyncfusionLicense.txt";

        using var stream = assembly.GetManifestResourceStream(resourceName);
        if (stream != null)
        {
            using var reader = new StreamReader(stream);
            return reader.ReadToEnd().Trim();
        }

        return null;
    }
}
```

#### 7.1.2 主题配置
```xml
<!-- App.xaml中的主题配置 -->
<Application x:Class="YourApp.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:syncfusion="http://schemas.syncfusion.com/wpf">

    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!-- Syncfusion主题资源 -->
                <ResourceDictionary Source="/Syncfusion.Themes.MaterialLight.WPF;component/MSControl/MSControl.xaml" />
                <ResourceDictionary Source="/Syncfusion.Themes.MaterialLight.WPF;component/SfDataGrid/SfDataGrid.xaml" />
                <ResourceDictionary Source="/Syncfusion.Themes.MaterialLight.WPF;component/SfChart/SfChart.xaml" />
                <ResourceDictionary Source="/Syncfusion.Themes.MaterialLight.WPF;component/SfRichTextBoxAdv/SfRichTextBoxAdv.xaml" />

                <!-- 自定义样式 -->
                <ResourceDictionary Source="Resources/Styles/CustomStyles.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <!-- 全局主题设置 -->
            <syncfusion:SkinStorage.VisualStyle>MaterialLight</syncfusion:SkinStorage.VisualStyle>
        </ResourceDictionary>
    </Application.Resources>
</Application>
```

### 7.2 常用控件集成和配置

#### 7.2.1 SfDataGrid高级配置
```xml
<!-- Views/CustomerListView.xaml -->
<syncfusion:SfDataGrid x:Name="CustomerDataGrid"
                       ItemsSource="{Binding Customers}"
                       SelectedItem="{Binding SelectedCustomer}"
                       AutoGenerateColumns="False"
                       AllowEditing="True"
                       AllowSorting="True"
                       AllowFiltering="True"
                       AllowGrouping="True"
                       AllowResizingColumns="True"
                       AllowDraggingColumns="True"
                       ShowGroupDropArea="True"
                       SelectionMode="Single"
                       NavigationMode="Cell"
                       EditTrigger="OnTap"
                       AddNewRowPosition="Top"
                       ValidationMode="InEdit"
                       GridValidationMode="InEdit">

    <!-- 列定义 -->
    <syncfusion:SfDataGrid.Columns>
        <!-- 文本列 -->
        <syncfusion:GridTextColumn HeaderText="客户姓名"
                                   MappingName="Name"
                                   Width="150"
                                   AllowEditing="True">
            <syncfusion:GridTextColumn.CellStyle>
                <Style TargetType="syncfusion:GridCell">
                    <Setter Property="FontWeight" Value="Bold"/>
                </Style>
            </syncfusion:GridTextColumn.CellStyle>
        </syncfusion:GridTextColumn>

        <!-- 邮箱列带验证 -->
        <syncfusion:GridTextColumn HeaderText="邮箱地址"
                                   MappingName="Email"
                                   Width="200">
            <syncfusion:GridTextColumn.CellTemplate>
                <DataTemplate>
                    <TextBlock Text="{Binding Email}">
                        <TextBlock.Style>
                            <Style TargetType="TextBlock">
                                <Style.Triggers>
                                    <DataTrigger Binding="{Binding IsEmailValid}" Value="False">
                                        <Setter Property="Foreground" Value="Red"/>
                                    </DataTrigger>
                                </Style.Triggers>
                            </Style>
                        </TextBlock.Style>
                    </TextBlock>
                </DataTemplate>
            </syncfusion:GridTextColumn.CellTemplate>
        </syncfusion:GridTextColumn>

        <!-- 日期列 -->
        <syncfusion:GridDateTimeColumn HeaderText="注册日期"
                                       MappingName="RegisterDate"
                                       Width="120"
                                       Pattern="ShortDate"/>

        <!-- 数值列 -->
        <syncfusion:GridNumericColumn HeaderText="订单数量"
                                      MappingName="OrderCount"
                                      Width="100"
                                      NumberDecimalDigits="0"/>

        <!-- 复选框列 -->
        <syncfusion:GridCheckBoxColumn HeaderText="活跃状态"
                                       MappingName="IsActive"
                                       Width="80"/>

        <!-- 下拉列表列 -->
        <syncfusion:GridComboBoxColumn HeaderText="客户类型"
                                       MappingName="CustomerType"
                                       Width="120"
                                       ItemsSource="{Binding CustomerTypes, Source={x:Static local:CustomerViewModel.Instance}}"/>

        <!-- 模板列 -->
        <syncfusion:GridTemplateColumn HeaderText="操作" Width="150">
            <syncfusion:GridTemplateColumn.CellTemplate>
                <DataTemplate>
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                        <Button Content="编辑"
                                Command="{Binding DataContext.EditCustomerCommand, RelativeSource={RelativeSource AncestorType=syncfusion:SfDataGrid}}"
                                CommandParameter="{Binding}"
                                Margin="2"/>
                        <Button Content="删除"
                                Command="{Binding DataContext.DeleteCustomerCommand, RelativeSource={RelativeSource AncestorType=syncfusion:SfDataGrid}}"
                                CommandParameter="{Binding}"
                                Margin="2"/>
                    </StackPanel>
                </DataTemplate>
            </syncfusion:GridTemplateColumn.CellTemplate>
        </syncfusion:GridTemplateColumn>
    </syncfusion:SfDataGrid.Columns>

    <!-- 分组配置 -->
    <syncfusion:SfDataGrid.GroupColumnDescriptions>
        <syncfusion:GroupColumnDescription ColumnName="CustomerType"/>
    </syncfusion:SfDataGrid.GroupColumnDescriptions>

    <!-- 排序配置 -->
    <syncfusion:SfDataGrid.SortColumnDescriptions>
        <syncfusion:SortColumnDescription ColumnName="Name" SortDirection="Ascending"/>
    </syncfusion:SfDataGrid.SortColumnDescriptions>

    <!-- 汇总行配置 -->
    <syncfusion:SfDataGrid.TableSummaryRows>
        <syncfusion:GridTableSummaryRow ShowSummaryInRow="False">
            <syncfusion:GridTableSummaryRow.SummaryColumns>
                <syncfusion:GridSummaryColumn Name="TotalCustomers"
                                              MappingName="Name"
                                              SummaryType="CountAggregate"
                                              Format="'总客户数: {Count}'"/>
                <syncfusion:GridSummaryColumn Name="TotalOrders"
                                              MappingName="OrderCount"
                                              SummaryType="SumAggregate"
                                              Format="'总订单: {Sum}'"/>
            </syncfusion:GridTableSummaryRow.SummaryColumns>
        </syncfusion:GridTableSummaryRow>
    </syncfusion:SfDataGrid.TableSummaryRows>
</syncfusion:SfDataGrid>
```

#### 7.2.2 SfChart图表配置
```xml
<!-- Views/ReportView.xaml -->
<syncfusion:SfChart x:Name="SalesChart"
                    Header="销售报表"
                    Legend="{Binding}"
                    PrimaryAxis="{Binding PrimaryAxis}"
                    SecondaryAxis="{Binding SecondaryAxis}">

    <!-- 图例配置 -->
    <syncfusion:SfChart.Legend>
        <syncfusion:ChartLegend DockPosition="Right"
                                IconVisibility="Visible"
                                ToggleSeriesVisibility="True"/>
    </syncfusion:SfChart.Legend>

    <!-- X轴配置 -->
    <syncfusion:SfChart.PrimaryAxis>
        <syncfusion:CategoryAxis Header="月份"
                                 LabelRotationAngle="45"
                                 ShowGridLines="False"/>
    </syncfusion:SfChart.PrimaryAxis>

    <!-- Y轴配置 -->
    <syncfusion:SfChart.SecondaryAxis>
        <syncfusion:NumericalAxis Header="销售额 (万元)"
                                  Minimum="0"
                                  Interval="10"
                                  LabelFormat="C0"/>
    </syncfusion:SfChart.SecondaryAxis>

    <!-- 数据系列 -->
    <syncfusion:ColumnSeries ItemsSource="{Binding MonthlySales}"
                             XBindingPath="Month"
                             YBindingPath="Amount"
                             Label="月度销售"
                             ShowTooltip="True">
        <syncfusion:ColumnSeries.AdornmentsInfo>
            <syncfusion:ChartAdornmentInfo ShowLabel="True"
                                           LabelPosition="Top"
                                           LabelFormat="#.##万"/>
        </syncfusion:ColumnSeries.AdornmentsInfo>
    </syncfusion:ColumnSeries>

    <syncfusion:LineSeries ItemsSource="{Binding MonthlyTarget}"
                           XBindingPath="Month"
                           YBindingPath="Target"
                           Label="目标销售"
                           StrokeDashArray="5,3">
        <syncfusion:LineSeries.AdornmentsInfo>
            <syncfusion:ChartAdornmentInfo ShowMarker="True"
                                           Symbol="Circle"
                                           MarkerHeight="8"
                                           MarkerWidth="8"/>
        </syncfusion:LineSeries.AdornmentsInfo>
    </syncfusion:LineSeries>
</syncfusion:SfChart>
```

### 7.3 性能优化技巧

#### 7.3.1 SfDataGrid性能优化
```csharp
```csharp
// ViewModels/CustomerListViewModel.cs - 针对大数据集的优化
public partial class CustomerListViewModel : ObservableObject
{
    private readonly ICustomerService _customerService;
    private readonly ILogger<CustomerListViewModel> _logger;

    public CustomerListViewModel(ICustomerService customerService, ILogger<CustomerListViewModel> logger)
    {
        _customerService = customerService;
        _logger = logger;

        // 使用虚拟化集合提高性能
        Customers = new VirtualizingObservableCollection<Customer>();

        // 配置分页
        PageSize = 100;
        CurrentPage = 1;
    }

    [ObservableProperty]
    private VirtualizingObservableCollection<Customer> _customers;

    [ObservableProperty]
    private int _pageSize;

    [ObservableProperty]
    private int _currentPage;

    [ObservableProperty]
    private int _totalRecords;

    [ObservableProperty]
    private bool _isLoading;

    // 分页加载数据
    [RelayCommand]
    private async Task LoadCustomersPage(int page = 1)
    {
        IsLoading = true;
        try
        {
            var result = await _customerService.GetCustomersPagedAsync(page, PageSize);

            if (page == 1)
            {
                Customers.Clear();
            }

            foreach (var customer in result.Items)
            {
                Customers.Add(customer);
            }

            TotalRecords = result.TotalCount;
            CurrentPage = page;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "加载客户数据失败");
        }
        finally
        {
            IsLoading = false;
        }
    }

    // 延迟搜索
    private CancellationTokenSource _searchCancellationTokenSource;

    [ObservableProperty]
    private string _searchText;

    partial void OnSearchTextChanged(string value)
    {
        // 取消之前的搜索
        _searchCancellationTokenSource?.Cancel();
        _searchCancellationTokenSource = new CancellationTokenSource();

        // 延迟500ms执行搜索
        _ = Task.Delay(500, _searchCancellationTokenSource.Token)
            .ContinueWith(async _ =>
            {
                if (!_searchCancellationTokenSource.Token.IsCancellationRequested)
                {
                    await SearchCustomersCommand.ExecuteAsync(_searchCancellationTokenSource.Token);
                }
            }, TaskScheduler.FromCurrentSynchronizationContext());
    }

    [RelayCommand]
    private async Task SearchCustomers(CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrWhiteSpace(SearchText))
        {
            await LoadCustomersPageCommand.ExecuteAsync(1);
            return;
        }

        IsLoading = true;
        try
        {
            var customers = await _customerService.SearchCustomersAsync(SearchText, cancellationToken);
            Customers.Clear();
            foreach (var customer in customers)
            {
                Customers.Add(customer);
            }
        }
        catch (OperationCanceledException)
        {
            // 搜索被取消，忽略
        }
        finally
        {
            IsLoading = false;
        }
    }
}

// 自定义虚拟化集合
public class VirtualizingObservableCollection<T> : ObservableCollection<T>
{
    private bool _suppressNotification = false;

    public void AddRange(IEnumerable<T> items)
    {
        _suppressNotification = true;
        foreach (var item in items)
        {
            Add(item);
        }
        _suppressNotification = false;
        OnCollectionChanged(new NotifyCollectionChangedEventArgs(NotifyCollectionChangedAction.Reset));
    }

    protected override void OnCollectionChanged(NotifyCollectionChangedEventArgs e)
    {
        if (!_suppressNotification)
        {
            base.OnCollectionChanged(e);
        }
    }
}
```

#### 7.3.2 SfChart性能优化
```csharp
// ViewModels/ReportViewModel.cs - 图表性能优化
public partial class ReportViewModel : ObservableObject
{
    [ObservableProperty]
    private ObservableCollection<SalesData> _monthlySales;

    [ObservableProperty]
    private bool _enableAnimation = false; // 大数据集时禁用动画

    [ObservableProperty]
    private bool _enableTooltip = true;

    // 数据抽样用于大数据集
    [RelayCommand]
    private async Task LoadSalesData()
    {
        var allData = await _reportService.GetSalesDataAsync();

        // 如果数据量大，进行抽样
        if (allData.Count > 1000)
        {
            EnableAnimation = false;
            EnableTooltip = false;

            // 每10个点取1个
            var sampledData = allData.Where((item, index) => index % 10 == 0).ToList();
            MonthlySales = new ObservableCollection<SalesData>(sampledData);
        }
        else
        {
            EnableAnimation = true;
            EnableTooltip = true;
            MonthlySales = new ObservableCollection<SalesData>(allData);
        }
    }
}
```

### 7.4 主题和外观定制

#### 7.4.1 自定义主题创建
```xml
<!-- Resources/Styles/SyncfusionCustomTheme.xaml -->
<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:syncfusion="http://schemas.syncfusion.com/wpf">

    <!-- 自定义SfDataGrid样式 -->
    <Style TargetType="syncfusion:SfDataGrid" x:Key="CustomDataGridStyle">
        <Setter Property="HeaderRowHeight" Value="40"/>
        <Setter Property="RowHeight" Value="35"/>
        <Setter Property="BorderBrush" Value="#E0E0E0"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="GridLinesVisibility" Value="Horizontal"/>
        <Setter Property="HeaderStyle">
            <Setter.Value>
                <Style TargetType="syncfusion:GridHeaderCellControl">
                    <Setter Property="Background" Value="#F5F5F5"/>
                    <Setter Property="FontWeight" Value="Bold"/>
                    <Setter Property="FontSize" Value="14"/>
                    <Setter Property="Foreground" Value="#333333"/>
                </Style>
            </Setter.Value>
        </Setter>
        <Setter Property="CellStyle">
            <Setter.Value>
                <Style TargetType="syncfusion:GridCell">
                    <Setter Property="BorderBrush" Value="#E8E8E8"/>
                    <Setter Property="FontSize" Value="13"/>
                    <Style.Triggers>
                        <Trigger Property="IsSelected" Value="True">
                            <Setter Property="Background" Value="#E3F2FD"/>
                        </Trigger>
                    </Style.Triggers>
                </Style>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 自定义SfChart样式 -->
    <Style TargetType="syncfusion:SfChart" x:Key="CustomChartStyle">
        <Setter Property="Background" Value="White"/>
        <Setter Property="BorderBrush" Value="#E0E0E0"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Margin" Value="10"/>
        <Setter Property="Padding" Value="10"/>
    </Style>

</ResourceDictionary>
```

### 7.5 部署和许可证管理

#### 7.5.1 生产环境部署配置
```csharp
// Configuration/SyncfusionConfiguration.cs
public static class SyncfusionConfiguration
{
    public static void ConfigureForProduction(IServiceCollection services, IConfiguration configuration)
    {
        // 注册许可证
        var licenseKey = configuration["Syncfusion:LicenseKey"]
            ?? Environment.GetEnvironmentVariable("SYNCFUSION_LICENSE_KEY");

        if (!string.IsNullOrEmpty(licenseKey))
        {
            Syncfusion.Licensing.SyncfusionLicenseProvider.RegisterLicense(licenseKey);
        }
        else
        {
            throw new InvalidOperationException("Syncfusion许可证未配置");
        }

        // 配置Syncfusion服务
        services.Configure<SyncfusionSettings>(configuration.GetSection("Syncfusion"));

        // 注册Syncfusion相关服务
        services.AddSingleton<ISyncfusionThemeService, SyncfusionThemeService>();
        services.AddSingleton<IExportService, SyncfusionExportService>();
    }
}

// Configuration/Settings/SyncfusionSettings.cs
public class SyncfusionSettings
{
    public string LicenseKey { get; set; }
    public string Theme { get; set; } = "MaterialLight";
    public bool EnableVirtualization { get; set; } = true;
    public int DefaultPageSize { get; set; } = 100;
    public bool EnableAnimation { get; set; } = true;
    public ExportSettings Export { get; set; } = new();
}

public class ExportSettings
{
    public bool EnableExcelExport { get; set; } = true;
    public bool EnablePdfExport { get; set; } = true;
    public string DefaultExportPath { get; set; } = "Exports";
    public int MaxExportRecords { get; set; } = 10000;
}
```

#### 7.5.2 许可证验证和错误处理
```csharp
// Services/Implementations/SyncfusionLicenseService.cs
public interface ISyncfusionLicenseService
{
    bool ValidateLicense();
    string GetLicenseStatus();
}

public class SyncfusionLicenseService : ISyncfusionLicenseService
{
    private readonly ILogger<SyncfusionLicenseService> _logger;
    private readonly SyncfusionSettings _settings;

    public SyncfusionLicenseService(
        ILogger<SyncfusionLicenseService> logger,
        IOptions<SyncfusionSettings> settings)
    {
        _logger = logger;
        _settings = settings.Value;
    }

    public bool ValidateLicense()
    {
        try
        {
            // 尝试创建一个Syncfusion控件来验证许可证
            var testGrid = new Syncfusion.UI.Xaml.Grid.SfDataGrid();

            // 检查是否显示许可证警告
            var licenseProvider = Syncfusion.Licensing.SyncfusionLicenseProvider.GetLicenseProvider();

            _logger.LogInformation("Syncfusion许可证验证成功");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Syncfusion许可证验证失败");
            return false;
        }
    }

    public string GetLicenseStatus()
    {
        try
        {
            // 获取许可证状态信息
            return "许可证有效";
        }
        catch
        {
            return "许可证无效或未配置";
        }
    }
}
```

## 8. 技术栈组合使用最佳实践

### 8.1 完整的应用程序架构示例

#### 8.1.1 主应用程序配置
```csharp
// App.xaml.cs - 完整的现代WPF应用配置
public partial class App : Application
{
    private IHost _host;

    protected override async void OnStartup(StartupEventArgs e)
    {
        try
        {
            // 创建Host Builder
            _host = CreateHostBuilder(e.Args).Build();

            // 启动Host
            await _host.StartAsync();

            // 验证Syncfusion许可证
            var licenseService = _host.Services.GetRequiredService<ISyncfusionLicenseService>();
            if (!licenseService.ValidateLicense())
            {
                MessageBox.Show("Syncfusion许可证验证失败", "许可证错误",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }

            // 创建并显示主窗口
            var mainWindow = _host.Services.GetRequiredService<MainWindow>();
            var mainViewModel = _host.Services.GetRequiredService<MainViewModel>();
            mainWindow.DataContext = mainViewModel;
            mainWindow.Show();

            base.OnStartup(e);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"应用程序启动失败: {ex.Message}", "启动错误",
                MessageBoxButton.OK, MessageBoxImage.Error);
            Shutdown(1);
        }
    }

    private IHostBuilder CreateHostBuilder(string[] args) =>
        Host.CreateDefaultBuilder(args)
            .ConfigureAppConfiguration((context, config) =>
            {
                config.SetBasePath(Directory.GetCurrentDirectory());
                config.AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);
                config.AddJsonFile($"appsettings.{context.HostingEnvironment.EnvironmentName}.json",
                    optional: true, reloadOnChange: true);
                config.AddEnvironmentVariables();
                config.AddCommandLine(args);
            })
            .ConfigureServices((context, services) =>
            {
                ConfigureServices(services, context.Configuration);
            })
            .ConfigureLogging((context, logging) =>
            {
                logging.ClearProviders();
                logging.AddConsole();
                logging.AddDebug();
                logging.AddFile(context.Configuration.GetSection("Logging"));
            });

    private void ConfigureServices(IServiceCollection services, IConfiguration configuration)
    {
        // 配置设置
        services.Configure<AppSettings>(configuration.GetSection("AppSettings"));
        services.Configure<DatabaseSettings>(configuration.GetSection("Database"));

        // 配置数据库
        services.AddDbContext<ApplicationDbContext>(options =>
        {
            var connectionString = configuration.GetConnectionString("DefaultConnection");
            options.UseSqlServer(connectionString);
            options.EnableSensitiveDataLogging(false);
            options.EnableDetailedErrors(false);
        });

        // 配置Syncfusion
        SyncfusionConfiguration.ConfigureForProduction(services, configuration);

        // 注册CommunityToolkit.Mvvm服务
        services.AddSingleton<IMessenger, WeakReferenceMessenger>();

        // 注册应用服务
        RegisterApplicationServices(services);

        // 注册ViewModels
        RegisterViewModels(services);

        // 注册Views
        RegisterViews(services);
    }

    private void RegisterApplicationServices(IServiceCollection services)
    {
        // 核心服务 - Singleton
        services.AddSingleton<ILoggingService, LoggingService>();
        services.AddSingleton<IConfigurationService, ConfigurationService>();
        services.AddSingleton<IEventAggregator, EventAggregator>();
        services.AddSingleton<ISyncfusionLicenseService, SyncfusionLicenseService>();

        // 业务服务 - Scoped
        services.AddScoped<ICustomerService, CustomerService>();
        services.AddScoped<IOrderService, OrderService>();
        services.AddScoped<IReportService, ReportService>();
        services.AddScoped<IExportService, SyncfusionExportService>();

        // UI服务 - Transient
        services.AddTransient<IDialogService, DialogService>();
        services.AddTransient<INavigationService, NavigationService>();
        services.AddTransient<IValidationService, ValidationService>();
        services.AddTransient<IViewModelFactory, ViewModelFactory>();
    }

    private void RegisterViewModels(IServiceCollection services)
    {
        services.AddTransient<MainViewModel>();
        services.AddTransient<CustomerListViewModel>();
        services.AddTransient<CustomerDetailViewModel>();
        services.AddTransient<OrderListViewModel>();
        services.AddTransient<ReportViewModel>();
        services.AddTransient<SettingsViewModel>();
    }

    private void RegisterViews(IServiceCollection services)
    {
        services.AddTransient<MainWindow>();
        services.AddTransient<CustomerListView>();
        services.AddTransient<CustomerDetailView>();
        services.AddTransient<OrderListView>();
        services.AddTransient<ReportView>();
        services.AddTransient<SettingsView>();
    }

    protected override async void OnExit(ExitEventArgs e)
    {
        if (_host != null)
        {
            await _host.StopAsync();
            _host.Dispose();
        }
        base.OnExit(e);
    }
}
```

## 9. 更新的AI助手指令模板

### 9.1 现代WPF开发指令
当AI助手处理WPF开发任务时，必须遵循以下更新的指令：

```
作为现代WPF开发专家，在生成代码时请严格遵循以下技术栈和规则：

## 技术栈要求
1. **依赖注入**: 必须使用Microsoft.Extensions.DependencyInjection
2. **MVVM框架**: 必须使用CommunityToolkit.Mvvm
3. **UI控件**: 优先使用Syncfusion WPF控件库
4. **数据访问**: 使用Entity Framework Core
5. **.NET版本**: 使用.NET 6/7/8

## 代码生成规则
1. **ViewModel设计**:
   - 继承自ObservableObject或ObservableRecipient
   - 使用[ObservableProperty]特性自动生成属性
   - 使用[RelayCommand]特性自动生成命令
   - 通过构造函数注入依赖服务

2. **服务注册**:
   - 在App.xaml.cs中配置完整的DI容器
   - 正确选择服务生命周期（Singleton/Scoped/Transient）
   - 注册Syncfusion许可证和主题

3. **XAML编写**:
   - 优先使用Syncfusion控件（SfDataGrid、SfChart等）
   - 正确配置数据绑定和命令绑定
   - 应用适当的样式和主题

4. **性能优化**:
   - 对大数据集启用虚拟化
   - 使用异步方法处理I/O操作
   - 实现适当的错误处理和日志记录

5. **项目结构**:
   - 遵循推荐的文件夹结构
   - 正确组织Views、ViewModels、Services
   - 使用配置文件管理应用设置
```

### 9.2 代码审查指令
```
在审查现代WPF代码时，请检查以下方面：

## 架构合规性
1. **依赖注入使用**:
   - 验证服务是否正确注册到DI容器
   - 检查生命周期选择是否合适
   - 确认ViewModels通过构造函数注入依赖

2. **CommunityToolkit.Mvvm使用**:
   - 验证是否使用源生成器特性
   - 检查ObservableObject/ObservableRecipient的正确使用
   - 确认Messenger模式的正确实现

3. **Syncfusion集成**:
   - 验证许可证是否正确配置
   - 检查控件配置是否遵循最佳实践
   - 确认性能优化措施是否到位

## 代码质量
1. **错误处理**: 检查异常处理和用户反馈
2. **性能**: 验证虚拟化和异步操作
3. **可测试性**: 确认代码可以进行单元测试
4. **可维护性**: 检查代码组织和文档
```

### 9.3 问题诊断指令
```
当遇到现代WPF开发问题时，请按以下步骤诊断：

## 依赖注入问题
1. 检查服务是否正确注册
2. 验证生命周期配置
3. 确认构造函数参数匹配

## CommunityToolkit.Mvvm问题
1. 验证partial类声明
2. 检查源生成器是否正常工作
3. 确认特性使用是否正确

## Syncfusion问题
1. 验证许可证配置
2. 检查主题资源引用
3. 确认控件版本兼容性

## 性能问题
1. 检查数据虚拟化配置
2. 分析内存使用情况
3. 优化数据绑定复杂度
```

## 10. 针对新技术栈的常见问题解决方案

### 10.1 Microsoft.Extensions.DependencyInjection问题

#### 问题1: 服务未注册错误
```csharp
// 错误: Unable to resolve service for type 'ICustomerService'
// 解决方案: 确保在ConfigureServices中正确注册服务
private void ConfigureServices(IServiceCollection services, IConfiguration configuration)
{
    // 正确注册服务
    services.AddScoped<ICustomerService, CustomerService>();

    // 如果服务有依赖，确保依赖也已注册
    services.AddDbContext<ApplicationDbContext>(options =>
        options.UseSqlServer(configuration.GetConnectionString("DefaultConnection")));
}
```

#### 问题2: 循环依赖
```csharp
// 错误: A circular dependency was detected
// 解决方案: 重新设计服务依赖关系或使用工厂模式
public interface IServiceFactory<T>
{
    T CreateService();
}

public class ServiceFactory<T> : IServiceFactory<T>
{
    private readonly IServiceProvider _serviceProvider;

    public ServiceFactory(IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider;
    }

    public T CreateService()
    {
        return _serviceProvider.GetRequiredService<T>();
    }
}
```

#### 问题3: ViewModel构造函数注入失败
```csharp
// 错误: ViewModel无法通过DI创建
// 解决方案: 使用ViewModelFactory或直接注册ViewModel
public class ViewModelLocator
{
    private readonly IServiceProvider _serviceProvider;

    public ViewModelLocator(IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider;
    }

    public T GetViewModel<T>() where T : class
    {
        return _serviceProvider.GetRequiredService<T>();
    }
}

// 在View的代码后置中
public partial class CustomerListView : UserControl
{
    public CustomerListView(CustomerListViewModel viewModel)
    {
        InitializeComponent();
        DataContext = viewModel;
    }
}
```

### 10.2 CommunityToolkit.Mvvm问题

#### 问题1: 源生成器不工作
```csharp
// 问题: [ObservableProperty]特性不生成属性
// 解决方案: 确保类声明为partial
public partial class CustomerViewModel : ObservableObject // 必须是partial
{
    [ObservableProperty]
    private string _name; // 字段名必须以下划线开头
}

// 检查项目文件是否包含正确的包引用
<PackageReference Include="CommunityToolkit.Mvvm" Version="8.2.0" />
<PackageReference Include="Microsoft.CodeAnalysis.Analyzers" Version="3.3.4" PrivateAssets="all" />
```

#### 问题2: RelayCommand不能执行
```csharp
// 问题: Command无法执行或CanExecute不工作
// 解决方案: 检查CanExecute方法命名和逻辑
public partial class CustomerViewModel : ObservableObject
{
    [ObservableProperty]
    private Customer _selectedCustomer;

    [RelayCommand(CanExecute = nameof(CanDeleteCustomer))]
    private async Task DeleteCustomer()
    {
        // 删除逻辑
    }

    // CanExecute方法必须返回bool
    private bool CanDeleteCustomer() => SelectedCustomer != null;

    // 当依赖属性变化时，需要通知Command状态变化
    partial void OnSelectedCustomerChanged(Customer value)
    {
        DeleteCustomerCommand.NotifyCanExecuteChanged();
    }
}
```

#### 问题3: Messenger消息不接收
```csharp
// 问题: 消息发送了但接收不到
// 解决方案: 确保正确实现IRecipient接口并激活
public partial class CustomerListViewModel : ObservableRecipient,
    IRecipient<CustomerUpdatedMessage>
{
    public CustomerListViewModel(IMessenger messenger) : base(messenger)
    {
        // 必须激活才能接收消息
        IsActive = true;
    }

    // 必须实现Receive方法
    public void Receive(CustomerUpdatedMessage message)
    {
        // 处理消息
        var customer = Customers.FirstOrDefault(c => c.Id == message.CustomerId);
        if (customer != null)
        {
            // 更新客户信息
        }
    }

    // 确保在适当时候停用
    protected override void OnDeactivated()
    {
        base.OnDeactivated();
        // 清理资源
    }
}
```

### 10.3 Syncfusion控件问题

#### 问题1: 许可证错误
```csharp
// 问题: Syncfusion控件显示许可证警告
// 解决方案: 正确配置许可证
public partial class App : Application
{
    protected override void OnStartup(StartupEventArgs e)
    {
        // 在使用任何Syncfusion控件之前注册许可证
        var licenseKey = "YOUR_LICENSE_KEY";
        Syncfusion.Licensing.SyncfusionLicenseProvider.RegisterLicense(licenseKey);

        base.OnStartup(e);
    }
}

// 生产环境从配置文件读取
var configuration = new ConfigurationBuilder()
    .AddJsonFile("appsettings.json")
    .Build();
var licenseKey = configuration["Syncfusion:LicenseKey"];
```

#### 问题2: SfDataGrid性能问题
```csharp
// 问题: 大数据集导致界面卡顿
// 解决方案: 启用虚拟化和分页
// XAML配置
<syncfusion:SfDataGrid ItemsSource="{Binding Customers}"
                       EnableDataVirtualization="True"
                       ScrollMode="Line"
                       AllowFiltering="True"
                       FilterMode="AdvancedFilter">

    <!-- 启用列虚拟化 -->
    <syncfusion:SfDataGrid.GridColumnSizer>
        <syncfusion:GridColumnSizer ColumnSizerMode="Star"/>
    </syncfusion:SfDataGrid.GridColumnSizer>
</syncfusion:SfDataGrid>

// ViewModel中实现分页
public partial class CustomerListViewModel : ObservableObject
{
    [ObservableProperty]
    private ObservableCollection<Customer> _customers = new();

    [ObservableProperty]
    private int _pageSize = 100;

    [RelayCommand]
    private async Task LoadPage(int pageNumber)
    {
        var pagedResult = await _customerService.GetCustomersPagedAsync(pageNumber, PageSize);

        // 使用AddRange避免频繁的UI更新
        Customers.Clear();
        foreach (var customer in pagedResult.Items)
        {
            Customers.Add(customer);
        }
    }
}
```

#### 问题3: 主题不生效
```xml
<!-- 问题: Syncfusion主题样式不应用 -->
<!-- 解决方案: 正确引用主题资源 -->
<Application.Resources>
    <ResourceDictionary>
        <ResourceDictionary.MergedDictionaries>
            <!-- 确保引用正确的主题程序集 -->
            <ResourceDictionary Source="/Syncfusion.Themes.MaterialLight.WPF;component/MSControl/MSControl.xaml" />
            <ResourceDictionary Source="/Syncfusion.Themes.MaterialLight.WPF;component/SfDataGrid/SfDataGrid.xaml" />

            <!-- 确保程序集版本匹配 -->
            <ResourceDictionary Source="pack://application:,,,/Syncfusion.SfGrid.WPF;component/Styles/Styles.xaml" />
        </ResourceDictionary.MergedDictionaries>

        <!-- 设置全局主题 -->
        <syncfusion:SkinStorage.VisualStyle>MaterialLight</syncfusion:SkinStorage.VisualStyle>
    </ResourceDictionary>
</Application.Resources>
```

### 10.4 技术栈集成问题

#### 问题1: Entity Framework与DI集成
```csharp
// 问题: DbContext生命周期管理
// 解决方案: 正确配置DbContext生命周期
private void ConfigureServices(IServiceCollection services, IConfiguration configuration)
{
    // 使用Scoped生命周期
    services.AddDbContext<ApplicationDbContext>(options =>
    {
        options.UseSqlServer(configuration.GetConnectionString("DefaultConnection"));

        // 开发环境启用敏感数据日志
        #if DEBUG
        options.EnableSensitiveDataLogging();
        options.EnableDetailedErrors();
        #endif
    });

    // 服务也使用Scoped确保与DbContext生命周期一致
    services.AddScoped<ICustomerService, CustomerService>();
}
```

#### 问题2: 异步操作和UI线程
```csharp
// 问题: 异步操作阻塞UI线程
// 解决方案: 正确使用async/await和ConfigureAwait
public partial class CustomerViewModel : ObservableObject
{
    [RelayCommand]
    private async Task LoadCustomers()
    {
        IsLoading = true;
        try
        {
            // 使用ConfigureAwait(false)避免死锁
            var customers = await _customerService
                .GetCustomersAsync()
                .ConfigureAwait(false);

            // 回到UI线程更新集合
            await Application.Current.Dispatcher.InvokeAsync(() =>
            {
                Customers.Clear();
                foreach (var customer in customers)
                {
                    Customers.Add(customer);
                }
            });
        }
        finally
        {
            IsLoading = false;
        }
    }
}
```

## 11. 最佳实践检查清单

### 11.1 现代WPF应用检查清单
- [ ] 使用Microsoft.Extensions.DependencyInjection进行依赖注入
- [ ] 使用CommunityToolkit.Mvvm的源生成器特性
- [ ] 正确配置Syncfusion许可证和主题
- [ ] 实现适当的错误处理和日志记录
- [ ] 使用异步方法处理I/O操作
- [ ] 为大数据集启用虚拟化
- [ ] 实现单元测试和集成测试
- [ ] 遵循MVVM模式和SOLID原则
- [ ] 使用配置文件管理应用设置
- [ ] 实现适当的资源释放和内存管理

---

*本文档专门针对现代WPF开发技术栈（Microsoft.Extensions.DependencyInjection + CommunityToolkit.Mvvm + Syncfusion）进行了优化，将持续更新以反映最新的最佳实践。*

## 9. AI助手指令模板

### 9.1 代码生成指令
当AI助手生成WPF代码时，必须遵循以下指令：

```
作为WPF开发专家，在生成代码时请严格遵循以下规则：

1. **项目结构规范**：
   - 将View文件放在Views文件夹
   - 将ViewModel文件放在ViewModels文件夹
   - 将Model文件放在Models文件夹
   - 将Service文件放在Services文件夹

2. **MVVM模式实施**：
   - 确保View不包含业务逻辑
   - ViewModel继承自ObservableObject或实现INotifyPropertyChanged
   - 使用Command模式处理用户交互
   - 通过依赖注入管理服务依赖

3. **命名约定**：
   - 类名使用PascalCase
   - 属性和方法使用PascalCase
   - 私有字段使用_camelCase
   - 常量使用UPPER_CASE

4. **性能优化**：
   - 对大数据集使用虚拟化
   - 实现适当的数据绑定模式
   - 避免在UI线程执行耗时操作
   - 使用异步方法处理I/O操作

5. **错误处理**：
   - 实现全局异常处理
   - 在ViewModel中提供用户友好的错误信息
   - 使用日志记录详细错误信息
   - 提供错误恢复机制

6. **代码质量**：
   - 添加适当的XML文档注释
   - 使用有意义的变量和方法名
   - 保持方法简洁（不超过20行）
   - 遵循SOLID原则
```

### 9.2 代码审查指令
```
在审查WPF代码时，请检查以下方面：

1. **架构合规性**：
   - 验证MVVM模式的正确实施
   - 检查依赖关系的合理性
   - 确认接口和抽象的适当使用

2. **性能考虑**：
   - 识别潜在的内存泄漏
   - 检查数据绑定的效率
   - 验证异步操作的正确实现

3. **可维护性**：
   - 评估代码的可读性
   - 检查重复代码
   - 验证单一职责原则的遵循

4. **测试覆盖**：
   - 确认关键业务逻辑有单元测试
   - 检查ViewModel的测试覆盖率
   - 验证集成测试的完整性

5. **安全性**：
   - 检查输入验证
   - 验证数据访问的安全性
   - 确认敏感信息的保护
```

### 9.3 问题诊断指令
```
当遇到WPF开发问题时，请按以下步骤诊断：

1. **数据绑定问题**：
   - 检查DataContext是否正确设置
   - 验证绑定路径的拼写
   - 确认属性变更通知的实现
   - 使用输出窗口查看绑定错误

2. **性能问题**：
   - 使用性能分析工具识别瓶颈
   - 检查UI线程的阻塞
   - 分析内存使用情况
   - 优化数据绑定和渲染

3. **布局问题**：
   - 验证Panel的选择是否合适
   - 检查尺寸和对齐设置
   - 确认Grid行列定义
   - 使用布局调试工具

4. **运行时错误**：
   - 分析异常堆栈跟踪
   - 检查资源引用
   - 验证类型转换
   - 确认线程安全性
```

## 10. 常见问题解决方案

### 10.1 数据绑定问题

**问题**: 数据绑定不工作
```csharp
// 错误示例
public class CustomerViewModel
{
    public string Name { get; set; } // 缺少属性变更通知
}

// 正确示例
public partial class CustomerViewModel : ObservableObject
{
    [ObservableProperty]
    private string _name;
}
```

**问题**: 集合绑定不更新UI
```csharp
// 错误示例
public List<Customer> Customers { get; set; } = new List<Customer>();

// 正确示例
public ObservableCollection<Customer> Customers { get; } = new ObservableCollection<Customer>();
```

### 10.2 性能问题

**问题**: 大数据集导致UI卡顿
```xml
<!-- 解决方案：启用虚拟化 -->
<ListView ItemsSource="{Binding Customers}"
          VirtualizingPanel.IsVirtualizing="True"
          VirtualizingPanel.VirtualizationMode="Recycling"
          ScrollViewer.CanContentScroll="True">
</ListView>
```

**问题**: 频繁的属性变更通知
```csharp
// 优化方案：批量更新
public void UpdateCustomer(string name, string email, string phone)
{
    _name = name;
    _email = email;
    _phone = phone;

    // 批量通知
    OnPropertyChanged(nameof(Name));
    OnPropertyChanged(nameof(Email));
    OnPropertyChanged(nameof(Phone));
}
```

### 10.3 内存泄漏问题

**问题**: 事件订阅导致内存泄漏
```csharp
// 解决方案：使用弱引用或及时取消订阅
public class CustomerViewModel : IDisposable
{
    private readonly ICustomerService _customerService;

    public CustomerViewModel(ICustomerService customerService)
    {
        _customerService = customerService;
        _customerService.CustomerUpdated += OnCustomerUpdated;
    }

    public void Dispose()
    {
        _customerService.CustomerUpdated -= OnCustomerUpdated;
    }

    private void OnCustomerUpdated(object sender, CustomerEventArgs e)
    {
        // 处理事件
    }
}
```

### 10.4 XAML编译错误

**问题**: 资源未找到
```xml
<!-- 确保资源字典正确引用 -->
<Application.Resources>
    <ResourceDictionary>
        <ResourceDictionary.MergedDictionaries>
            <ResourceDictionary Source="Resources/Styles/ButtonStyles.xaml"/>
        </ResourceDictionary.MergedDictionaries>
    </ResourceDictionary>
</Application.Resources>
```

**问题**: 数据模板类型不匹配
```xml
<!-- 确保DataType正确指定 -->
<DataTemplate DataType="{x:Type local:Customer}">
    <StackPanel>
        <TextBlock Text="{Binding Name}"/>
        <TextBlock Text="{Binding Email}"/>
    </StackPanel>
</DataTemplate>
```

## 11. 最佳实践检查清单

### 11.1 代码质量检查
- [ ] 所有公共API都有XML文档注释
- [ ] 方法长度不超过20行
- [ ] 类的职责单一且明确
- [ ] 使用有意义的命名
- [ ] 避免魔法数字和字符串

### 11.2 MVVM模式检查
- [ ] View不包含业务逻辑
- [ ] ViewModel不直接引用View
- [ ] 使用Command处理用户交互
- [ ] 数据绑定使用适当的模式
- [ ] 实现属性变更通知

### 11.3 性能检查
- [ ] 大数据集使用虚拟化
- [ ] 避免在UI线程执行耗时操作
- [ ] 使用异步方法处理I/O
- [ ] 实现适当的缓存策略
- [ ] 及时释放资源

### 11.4 测试检查
- [ ] 关键业务逻辑有单元测试
- [ ] ViewModel有完整的测试覆盖
- [ ] 集成测试验证端到端功能
- [ ] UI自动化测试覆盖主要用户场景

---

*本文档将持续更新，以反映WPF开发的最新最佳实践和技术发展。*
