# WPF开发AI辅助规则文档

## 概述
本文档为AI助手提供WPF（Windows Presentation Foundation）开发的专业指导规则，确保生成高质量、符合最佳实践的WPF应用程序代码。

## 1. WPF项目结构规范

### 1.1 推荐文件夹结构
```
ProjectName/
├── Views/                  # 视图文件（.xaml）
│   ├── MainWindow.xaml
│   ├── UserControls/      # 用户控件
│   └── Pages/             # 页面控件
├── ViewModels/            # 视图模型
│   ├── MainViewModel.cs
│   └── Base/              # 基础ViewModel类
├── Models/                # 数据模型
│   ├── Entities/          # 实体类
│   └── DTOs/              # 数据传输对象
├── Services/              # 业务服务
│   ├── Interfaces/        # 服务接口
│   └── Implementations/   # 服务实现
├── Resources/             # 资源文件
│   ├── Styles/            # 样式文件
│   ├── Templates/         # 模板文件
│   ├── Images/            # 图片资源
│   └── Dictionaries/      # 资源字典
├── Converters/            # 值转换器
├── Behaviors/             # 行为类
├── Controls/              # 自定义控件
└── Utilities/             # 工具类
```

### 1.2 命名约定
- **Views**: 使用PascalCase，以功能描述命名（如`CustomerListView.xaml`）
- **ViewModels**: 对应View名称 + "ViewModel"（如`CustomerListViewModel.cs`）
- **Models**: 使用PascalCase，描述性名称（如`Customer.cs`）
- **Services**: 接口以"I"开头，实现类以"Service"结尾（如`ICustomerService.cs`, `CustomerService.cs`）

### 1.3 代码组织原则
- 每个View对应一个ViewModel
- 复杂的业务逻辑放在Service层
- 使用依赖注入管理对象生命周期
- 资源文件按功能模块组织

## 2. MVVM模式实施指南

### 2.1 ViewModel设计原则
```csharp
// 基础ViewModel示例
public abstract class ViewModelBase : INotifyPropertyChanged
{
    public event PropertyChangedEventHandler PropertyChanged;
    
    protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }
    
    protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string propertyName = null)
    {
        if (EqualityComparer<T>.Default.Equals(field, value)) return false;
        field = value;
        OnPropertyChanged(propertyName);
        return true;
    }
}
```

### 2.2 数据绑定最佳实践
- 使用`INotifyPropertyChanged`接口实现属性变更通知
- 优先使用双向绑定（`{Binding Property, Mode=TwoWay}`）
- 对于集合使用`ObservableCollection<T>`
- 使用`ICollectionView`进行数据过滤和排序

### 2.3 Command模式规范
```csharp
// 使用CommunityToolkit.Mvvm的RelayCommand
[RelayCommand]
private async Task SaveCustomer()
{
    // 保存逻辑
}

// 带参数的Command
[RelayCommand]
private void DeleteCustomer(Customer customer)
{
    // 删除逻辑
}
```

### 2.4 依赖注入集成
- 推荐使用Microsoft.Extensions.DependencyInjection
- 在App.xaml.cs中配置服务容器
- ViewModel通过构造函数注入依赖服务

## 3. XAML编写规范

### 3.1 控件布局标准
```xml
<!-- 使用Grid进行复杂布局 -->
<Grid>
    <Grid.RowDefinitions>
        <RowDefinition Height="Auto"/>
        <RowDefinition Height="*"/>
        <RowDefinition Height="Auto"/>
    </Grid.RowDefinitions>
    
    <!-- 标题栏 -->
    <TextBlock Grid.Row="0" 
               Text="{Binding Title}" 
               Style="{StaticResource HeaderTextStyle}"/>
    
    <!-- 内容区域 -->
    <ScrollViewer Grid.Row="1">
        <!-- 内容 -->
    </ScrollViewer>
    
    <!-- 按钮栏 -->
    <StackPanel Grid.Row="2" 
                Orientation="Horizontal" 
                HorizontalAlignment="Right">
        <Button Content="保存" Command="{Binding SaveCommand}"/>
        <Button Content="取消" Command="{Binding CancelCommand}"/>
    </StackPanel>
</Grid>
```

### 3.2 样式定义规范
```xml
<!-- 在ResourceDictionary中定义样式 -->
<Style x:Key="HeaderTextStyle" TargetType="TextBlock">
    <Setter Property="FontSize" Value="18"/>
    <Setter Property="FontWeight" Value="Bold"/>
    <Setter Property="Margin" Value="0,0,0,10"/>
    <Setter Property="Foreground" Value="{DynamicResource PrimaryBrush}"/>
</Style>
```

### 3.3 数据模板设计
```xml
<DataTemplate x:Key="CustomerItemTemplate" DataType="{x:Type models:Customer}">
    <Border BorderBrush="Gray" BorderThickness="1" Margin="2" Padding="5">
        <StackPanel>
            <TextBlock Text="{Binding Name}" FontWeight="Bold"/>
            <TextBlock Text="{Binding Email}" Foreground="Gray"/>
        </StackPanel>
    </Border>
</DataTemplate>
```

## 4. 性能优化指导

### 4.1 数据虚拟化
- 对于大数据集使用`VirtualizingStackPanel`
- 启用容器回收：`VirtualizingPanel.VirtualizationMode="Recycling"`
- 使用`ListView`或`DataGrid`的内置虚拟化功能

### 4.2 内存泄漏预防
- 及时取消事件订阅
- 使用弱引用处理长生命周期对象
- 正确处理`IDisposable`对象
- 避免在ViewModel中直接引用View

### 4.3 渲染性能优化
- 减少复杂的视觉效果和动画
- 使用`BitmapCache`缓存复杂控件
- 避免频繁的布局更新
- 使用`Freezable`对象提高性能

## 5. 常用第三方库集成

### 5.1 CommunityToolkit.Mvvm配置
```csharp
// 完整的ViewModel示例
public partial class CustomerViewModel : ObservableObject
{
    private readonly ICustomerService _customerService;

    public CustomerViewModel(ICustomerService customerService)
    {
        _customerService = customerService;
        Customers = new ObservableCollection<Customer>();
    }

    [ObservableProperty]
    private string _customerName;

    [ObservableProperty]
    private Customer _selectedCustomer;

    [ObservableProperty]
    private bool _isLoading;

    public ObservableCollection<Customer> Customers { get; }

    [RelayCommand]
    private async Task LoadCustomers()
    {
        IsLoading = true;
        try
        {
            var customers = await _customerService.GetCustomersAsync();
            Customers.Clear();
            foreach (var customer in customers)
            {
                Customers.Add(customer);
            }
        }
        finally
        {
            IsLoading = false;
        }
    }

    [RelayCommand(CanExecute = nameof(CanSaveCustomer))]
    private async Task SaveCustomer()
    {
        if (SelectedCustomer != null)
        {
            await _customerService.SaveCustomerAsync(SelectedCustomer);
        }
    }

    private bool CanSaveCustomer() => SelectedCustomer != null && !string.IsNullOrEmpty(CustomerName);
}
```

### 5.2 Prism框架集成
```csharp
// App.xaml.cs - Prism应用程序配置
public partial class App : PrismApplication
{
    protected override Window CreateShell()
    {
        return Container.Resolve<MainWindow>();
    }

    protected override void RegisterTypes(IContainerRegistry containerRegistry)
    {
        // 注册服务
        containerRegistry.RegisterSingleton<ICustomerService, CustomerService>();
        containerRegistry.RegisterSingleton<IDialogService, DialogService>();

        // 注册ViewModels
        containerRegistry.Register<CustomerListViewModel>();
        containerRegistry.Register<CustomerDetailViewModel>();

        // 注册Views用于导航
        containerRegistry.RegisterForNavigation<CustomerListView, CustomerListViewModel>();
        containerRegistry.RegisterForNavigation<CustomerDetailView, CustomerDetailViewModel>();
    }

    protected override void ConfigureModuleCatalog(IModuleCatalog moduleCatalog)
    {
        moduleCatalog.AddModule<CustomerModule>();
        moduleCatalog.AddModule<OrderModule>();
    }
}

// 模块定义
public class CustomerModule : IModule
{
    public void OnInitialized(IContainerProvider containerProvider)
    {
        var regionManager = containerProvider.Resolve<IRegionManager>();
        regionManager.RequestNavigate("ContentRegion", "CustomerListView");
    }

    public void RegisterTypes(IContainerRegistry containerRegistry)
    {
        containerRegistry.RegisterForNavigation<CustomerListView>();
        containerRegistry.RegisterForNavigation<CustomerDetailView>();
    }
}
```

### 5.3 Entity Framework Core集成
```csharp
// DbContext配置
public class ApplicationDbContext : DbContext
{
    public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) : base(options) { }

    public DbSet<Customer> Customers { get; set; }
    public DbSet<Order> Orders { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<Customer>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
            entity.Property(e => e.Email).HasMaxLength(255);
        });

        modelBuilder.Entity<Order>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.HasOne(e => e.Customer)
                  .WithMany(c => c.Orders)
                  .HasForeignKey(e => e.CustomerId);
        });
    }
}

// 服务实现
public class CustomerService : ICustomerService
{
    private readonly ApplicationDbContext _context;

    public CustomerService(ApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<IEnumerable<Customer>> GetCustomersAsync()
    {
        return await _context.Customers
            .Include(c => c.Orders)
            .ToListAsync();
    }

    public async Task<Customer> GetCustomerByIdAsync(int id)
    {
        return await _context.Customers
            .Include(c => c.Orders)
            .FirstOrDefaultAsync(c => c.Id == id);
    }

    public async Task SaveCustomerAsync(Customer customer)
    {
        if (customer.Id == 0)
        {
            _context.Customers.Add(customer);
        }
        else
        {
            _context.Customers.Update(customer);
        }

        await _context.SaveChangesAsync();
    }
}
```

### 5.4 MaterialDesignInXaml集成
```xml
<!-- App.xaml中引用Material Design资源 -->
<Application.Resources>
    <ResourceDictionary>
        <ResourceDictionary.MergedDictionaries>
            <materialDesign:BundledTheme BaseTheme="Light" PrimaryColor="DeepPurple" SecondaryColor="Lime" />
            <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml" />
        </ResourceDictionary.MergedDictionaries>
    </ResourceDictionary>
</Application.Resources>

<!-- 使用Material Design控件 -->
<materialDesign:Card Padding="32" Margin="16">
    <StackPanel>
        <TextBox materialDesign:HintAssist.Hint="客户姓名"
                 Text="{Binding CustomerName}"
                 Style="{StaticResource MaterialDesignFloatingHintTextBox}" />

        <TextBox materialDesign:HintAssist.Hint="电子邮箱"
                 Text="{Binding Email}"
                 Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                 Margin="0,16,0,0" />

        <Button Content="保存"
                Command="{Binding SaveCommand}"
                Style="{StaticResource MaterialDesignRaisedButton}"
                Margin="0,16,0,0" />
    </StackPanel>
</materialDesign:Card>
```

## 6. 高级XAML技巧和自定义控件

### 6.1 值转换器（Value Converters）
```csharp
// 布尔值到可见性转换器
public class BooleanToVisibilityConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is bool boolValue)
        {
            return boolValue ? Visibility.Visible : Visibility.Collapsed;
        }
        return Visibility.Collapsed;
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        return value is Visibility visibility && visibility == Visibility.Visible;
    }
}

// 多值转换器示例
public class MultiValueConverter : IMultiValueConverter
{
    public object Convert(object[] values, Type targetType, object parameter, CultureInfo culture)
    {
        if (values.Length == 2 && values[0] is string firstName && values[1] is string lastName)
        {
            return $"{firstName} {lastName}";
        }
        return string.Empty;
    }

    public object[] ConvertBack(object value, Type[] targetTypes, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}
```

### 6.2 自定义控件开发
```csharp
// 自定义控件示例
[TemplatePart(Name = "PART_TextBox", Type = typeof(TextBox))]
[TemplatePart(Name = "PART_Button", Type = typeof(Button))]
public class SearchBox : Control
{
    private TextBox _textBox;
    private Button _button;

    static SearchBox()
    {
        DefaultStyleKeyProperty.OverrideMetadata(typeof(SearchBox),
            new FrameworkPropertyMetadata(typeof(SearchBox)));
    }

    public static readonly DependencyProperty SearchTextProperty =
        DependencyProperty.Register(nameof(SearchText), typeof(string), typeof(SearchBox),
            new FrameworkPropertyMetadata(string.Empty, FrameworkPropertyMetadataOptions.BindsTwoWayByDefault));

    public string SearchText
    {
        get => (string)GetValue(SearchTextProperty);
        set => SetValue(SearchTextProperty, value);
    }

    public static readonly DependencyProperty SearchCommandProperty =
        DependencyProperty.Register(nameof(SearchCommand), typeof(ICommand), typeof(SearchBox));

    public ICommand SearchCommand
    {
        get => (ICommand)GetValue(SearchCommandProperty);
        set => SetValue(SearchCommandProperty, value);
    }

    public override void OnApplyTemplate()
    {
        base.OnApplyTemplate();

        _textBox = GetTemplateChild("PART_TextBox") as TextBox;
        _button = GetTemplateChild("PART_Button") as Button;

        if (_button != null)
        {
            _button.Click += OnSearchButtonClick;
        }

        if (_textBox != null)
        {
            _textBox.KeyDown += OnTextBoxKeyDown;
        }
    }

    private void OnSearchButtonClick(object sender, RoutedEventArgs e)
    {
        ExecuteSearch();
    }

    private void OnTextBoxKeyDown(object sender, KeyEventArgs e)
    {
        if (e.Key == Key.Enter)
        {
            ExecuteSearch();
        }
    }

    private void ExecuteSearch()
    {
        SearchCommand?.Execute(SearchText);
    }
}
```

### 6.3 附加属性（Attached Properties）
```csharp
public static class TextBoxHelper
{
    public static readonly DependencyProperty WatermarkProperty =
        DependencyProperty.RegisterAttached("Watermark", typeof(string), typeof(TextBoxHelper),
            new PropertyMetadata(string.Empty, OnWatermarkChanged));

    public static string GetWatermark(DependencyObject obj)
    {
        return (string)obj.GetValue(WatermarkProperty);
    }

    public static void SetWatermark(DependencyObject obj, string value)
    {
        obj.SetValue(WatermarkProperty, value);
    }

    private static void OnWatermarkChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is TextBox textBox)
        {
            textBox.GotFocus -= TextBox_GotFocus;
            textBox.LostFocus -= TextBox_LostFocus;

            if (!string.IsNullOrEmpty(e.NewValue?.ToString()))
            {
                textBox.GotFocus += TextBox_GotFocus;
                textBox.LostFocus += TextBox_LostFocus;
                UpdateWatermark(textBox);
            }
        }
    }

    private static void TextBox_GotFocus(object sender, RoutedEventArgs e)
    {
        var textBox = sender as TextBox;
        if (textBox?.Text == GetWatermark(textBox))
        {
            textBox.Text = string.Empty;
            textBox.Foreground = SystemColors.WindowTextBrush;
        }
    }

    private static void TextBox_LostFocus(object sender, RoutedEventArgs e)
    {
        UpdateWatermark(sender as TextBox);
    }

    private static void UpdateWatermark(TextBox textBox)
    {
        if (string.IsNullOrEmpty(textBox?.Text))
        {
            textBox.Text = GetWatermark(textBox);
            textBox.Foreground = SystemColors.GrayTextBrush;
        }
    }
}
```

## 7. 调试和测试策略

### 7.1 单元测试方法
```csharp
// ViewModel单元测试示例
[TestClass]
public class CustomerViewModelTests
{
    private Mock<ICustomerService> _mockCustomerService;
    private CustomerViewModel _viewModel;

    [TestInitialize]
    public void Setup()
    {
        _mockCustomerService = new Mock<ICustomerService>();
        _viewModel = new CustomerViewModel(_mockCustomerService.Object);
    }

    [TestMethod]
    public async Task LoadCustomers_ShouldPopulateCustomersCollection()
    {
        // Arrange
        var customers = new List<Customer>
        {
            new Customer { Id = 1, Name = "John Doe" },
            new Customer { Id = 2, Name = "Jane Smith" }
        };
        _mockCustomerService.Setup(s => s.GetCustomersAsync()).ReturnsAsync(customers);

        // Act
        await _viewModel.LoadCustomersCommand.ExecuteAsync(null);

        // Assert
        Assert.AreEqual(2, _viewModel.Customers.Count);
        Assert.AreEqual("John Doe", _viewModel.Customers[0].Name);
    }

    [TestMethod]
    public void SaveCommand_WhenSelectedCustomerIsNull_ShouldNotExecute()
    {
        // Arrange
        _viewModel.SelectedCustomer = null;

        // Act & Assert
        Assert.IsFalse(_viewModel.SaveCustomerCommand.CanExecute(null));
    }

    [TestMethod]
    public void CustomerName_WhenChanged_ShouldRaisePropertyChanged()
    {
        // Arrange
        var propertyChangedRaised = false;
        _viewModel.PropertyChanged += (s, e) =>
        {
            if (e.PropertyName == nameof(_viewModel.CustomerName))
                propertyChangedRaised = true;
        };

        // Act
        _viewModel.CustomerName = "New Name";

        // Assert
        Assert.IsTrue(propertyChangedRaised);
    }
}
```

### 7.2 UI自动化测试
```csharp
// UI自动化测试示例
[TestClass]
public class MainWindowUITests
{
    private Application _application;
    private Window _mainWindow;

    [TestInitialize]
    public void Setup()
    {
        _application = new App();
        _application.InitializeComponent();
        _mainWindow = _application.MainWindow;
        _mainWindow.Show();
    }

    [TestCleanup]
    public void Cleanup()
    {
        _mainWindow?.Close();
        _application?.Shutdown();
    }

    [TestMethod]
    public void ClickSaveButton_ShouldTriggerSaveCommand()
    {
        // 使用AutomationId查找控件
        var saveButton = FindElementByAutomationId("SaveButton");
        Assert.IsNotNull(saveButton);

        // 模拟点击
        var invokePattern = saveButton.GetCurrentPattern(InvokePattern.Pattern) as InvokePattern;
        invokePattern?.Invoke();

        // 验证结果
        // 这里可以检查数据是否保存成功
    }

    private AutomationElement FindElementByAutomationId(string automationId)
    {
        var windowElement = AutomationElement.FromHandle(_mainWindow.Handle);
        return windowElement.FindFirst(TreeScope.Descendants,
            new PropertyCondition(AutomationElement.AutomationIdProperty, automationId));
    }
}
```

### 7.3 集成测试策略
```csharp
// 集成测试示例
[TestClass]
public class CustomerServiceIntegrationTests
{
    private ApplicationDbContext _context;
    private CustomerService _customerService;

    [TestInitialize]
    public void Setup()
    {
        var options = new DbContextOptionsBuilder<ApplicationDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;

        _context = new ApplicationDbContext(options);
        _customerService = new CustomerService(_context);
    }

    [TestMethod]
    public async Task SaveCustomer_ShouldPersistToDatabase()
    {
        // Arrange
        var customer = new Customer { Name = "Test Customer", Email = "<EMAIL>" };

        // Act
        await _customerService.SaveCustomerAsync(customer);

        // Assert
        var savedCustomer = await _context.Customers.FirstOrDefaultAsync(c => c.Name == "Test Customer");
        Assert.IsNotNull(savedCustomer);
        Assert.AreEqual("<EMAIL>", savedCustomer.Email);
    }

    [TestCleanup]
    public void Cleanup()
    {
        _context?.Dispose();
    }
}
```

## 8. 错误处理和日志记录

### 8.1 全局异常处理
```csharp
// App.xaml.cs中的全局异常处理
public partial class App : Application
{
    protected override void OnStartup(StartupEventArgs e)
    {
        // 设置全局异常处理
        DispatcherUnhandledException += App_DispatcherUnhandledException;
        AppDomain.CurrentDomain.UnhandledException += CurrentDomain_UnhandledException;
        TaskScheduler.UnobservedTaskException += TaskScheduler_UnobservedTaskException;

        base.OnStartup(e);
    }

    private void App_DispatcherUnhandledException(object sender, DispatcherUnhandledExceptionEventArgs e)
    {
        LogException(e.Exception);
        ShowErrorDialog("应用程序遇到错误", e.Exception.Message);
        e.Handled = true;
    }

    private void CurrentDomain_UnhandledException(object sender, UnhandledExceptionEventArgs e)
    {
        if (e.ExceptionObject is Exception exception)
        {
            LogException(exception);
            ShowErrorDialog("严重错误", exception.Message);
        }
    }

    private void TaskScheduler_UnobservedTaskException(object sender, UnobservedTaskExceptionEventArgs e)
    {
        LogException(e.Exception);
        e.SetObserved();
    }

    private void LogException(Exception exception)
    {
        // 使用日志框架记录异常
        // 例如：_logger.LogError(exception, "Unhandled exception occurred");
    }

    private void ShowErrorDialog(string title, string message)
    {
        MessageBox.Show(message, title, MessageBoxButton.OK, MessageBoxImage.Error);
    }
}
```

### 8.2 ViewModel中的错误处理
```csharp
public partial class CustomerViewModel : ObservableObject
{
    [ObservableProperty]
    private string _errorMessage;

    [ObservableProperty]
    private bool _hasError;

    [RelayCommand]
    private async Task LoadCustomers()
    {
        try
        {
            ClearError();
            IsLoading = true;

            var customers = await _customerService.GetCustomersAsync();
            Customers.Clear();
            foreach (var customer in customers)
            {
                Customers.Add(customer);
            }
        }
        catch (Exception ex)
        {
            SetError("加载客户数据时发生错误", ex);
        }
        finally
        {
            IsLoading = false;
        }
    }

    private void SetError(string message, Exception exception = null)
    {
        ErrorMessage = message;
        HasError = true;

        // 记录详细错误信息
        _logger?.LogError(exception, message);
    }

    private void ClearError()
    {
        ErrorMessage = string.Empty;
        HasError = false;
    }
}
```

### 8.3 用户友好的错误显示
```xml
<!-- 在View中显示错误信息 -->
<Grid>
    <!-- 错误信息显示区域 -->
    <Border Background="LightCoral"
            Padding="10"
            Margin="0,0,0,10"
            Visibility="{Binding HasError, Converter={StaticResource BooleanToVisibilityConverter}}">
        <StackPanel Orientation="Horizontal">
            <TextBlock Text="⚠"
                       FontSize="16"
                       Foreground="White"
                       Margin="0,0,10,0"/>
            <TextBlock Text="{Binding ErrorMessage}"
                       Foreground="White"
                       TextWrapping="Wrap"/>
            <Button Content="×"
                    Background="Transparent"
                    BorderThickness="0"
                    Foreground="White"
                    Command="{Binding ClearErrorCommand}"
                    Margin="10,0,0,0"/>
        </StackPanel>
    </Border>

    <!-- 主要内容区域 -->
    <ContentPresenter Content="{Binding}"/>
</Grid>
```

## 9. AI助手指令模板

### 9.1 代码生成指令
当AI助手生成WPF代码时，必须遵循以下指令：

```
作为WPF开发专家，在生成代码时请严格遵循以下规则：

1. **项目结构规范**：
   - 将View文件放在Views文件夹
   - 将ViewModel文件放在ViewModels文件夹
   - 将Model文件放在Models文件夹
   - 将Service文件放在Services文件夹

2. **MVVM模式实施**：
   - 确保View不包含业务逻辑
   - ViewModel继承自ObservableObject或实现INotifyPropertyChanged
   - 使用Command模式处理用户交互
   - 通过依赖注入管理服务依赖

3. **命名约定**：
   - 类名使用PascalCase
   - 属性和方法使用PascalCase
   - 私有字段使用_camelCase
   - 常量使用UPPER_CASE

4. **性能优化**：
   - 对大数据集使用虚拟化
   - 实现适当的数据绑定模式
   - 避免在UI线程执行耗时操作
   - 使用异步方法处理I/O操作

5. **错误处理**：
   - 实现全局异常处理
   - 在ViewModel中提供用户友好的错误信息
   - 使用日志记录详细错误信息
   - 提供错误恢复机制

6. **代码质量**：
   - 添加适当的XML文档注释
   - 使用有意义的变量和方法名
   - 保持方法简洁（不超过20行）
   - 遵循SOLID原则
```

### 9.2 代码审查指令
```
在审查WPF代码时，请检查以下方面：

1. **架构合规性**：
   - 验证MVVM模式的正确实施
   - 检查依赖关系的合理性
   - 确认接口和抽象的适当使用

2. **性能考虑**：
   - 识别潜在的内存泄漏
   - 检查数据绑定的效率
   - 验证异步操作的正确实现

3. **可维护性**：
   - 评估代码的可读性
   - 检查重复代码
   - 验证单一职责原则的遵循

4. **测试覆盖**：
   - 确认关键业务逻辑有单元测试
   - 检查ViewModel的测试覆盖率
   - 验证集成测试的完整性

5. **安全性**：
   - 检查输入验证
   - 验证数据访问的安全性
   - 确认敏感信息的保护
```

### 9.3 问题诊断指令
```
当遇到WPF开发问题时，请按以下步骤诊断：

1. **数据绑定问题**：
   - 检查DataContext是否正确设置
   - 验证绑定路径的拼写
   - 确认属性变更通知的实现
   - 使用输出窗口查看绑定错误

2. **性能问题**：
   - 使用性能分析工具识别瓶颈
   - 检查UI线程的阻塞
   - 分析内存使用情况
   - 优化数据绑定和渲染

3. **布局问题**：
   - 验证Panel的选择是否合适
   - 检查尺寸和对齐设置
   - 确认Grid行列定义
   - 使用布局调试工具

4. **运行时错误**：
   - 分析异常堆栈跟踪
   - 检查资源引用
   - 验证类型转换
   - 确认线程安全性
```

## 10. 常见问题解决方案

### 10.1 数据绑定问题

**问题**: 数据绑定不工作
```csharp
// 错误示例
public class CustomerViewModel
{
    public string Name { get; set; } // 缺少属性变更通知
}

// 正确示例
public partial class CustomerViewModel : ObservableObject
{
    [ObservableProperty]
    private string _name;
}
```

**问题**: 集合绑定不更新UI
```csharp
// 错误示例
public List<Customer> Customers { get; set; } = new List<Customer>();

// 正确示例
public ObservableCollection<Customer> Customers { get; } = new ObservableCollection<Customer>();
```

### 10.2 性能问题

**问题**: 大数据集导致UI卡顿
```xml
<!-- 解决方案：启用虚拟化 -->
<ListView ItemsSource="{Binding Customers}"
          VirtualizingPanel.IsVirtualizing="True"
          VirtualizingPanel.VirtualizationMode="Recycling"
          ScrollViewer.CanContentScroll="True">
</ListView>
```

**问题**: 频繁的属性变更通知
```csharp
// 优化方案：批量更新
public void UpdateCustomer(string name, string email, string phone)
{
    _name = name;
    _email = email;
    _phone = phone;

    // 批量通知
    OnPropertyChanged(nameof(Name));
    OnPropertyChanged(nameof(Email));
    OnPropertyChanged(nameof(Phone));
}
```

### 10.3 内存泄漏问题

**问题**: 事件订阅导致内存泄漏
```csharp
// 解决方案：使用弱引用或及时取消订阅
public class CustomerViewModel : IDisposable
{
    private readonly ICustomerService _customerService;

    public CustomerViewModel(ICustomerService customerService)
    {
        _customerService = customerService;
        _customerService.CustomerUpdated += OnCustomerUpdated;
    }

    public void Dispose()
    {
        _customerService.CustomerUpdated -= OnCustomerUpdated;
    }

    private void OnCustomerUpdated(object sender, CustomerEventArgs e)
    {
        // 处理事件
    }
}
```

### 10.4 XAML编译错误

**问题**: 资源未找到
```xml
<!-- 确保资源字典正确引用 -->
<Application.Resources>
    <ResourceDictionary>
        <ResourceDictionary.MergedDictionaries>
            <ResourceDictionary Source="Resources/Styles/ButtonStyles.xaml"/>
        </ResourceDictionary.MergedDictionaries>
    </ResourceDictionary>
</Application.Resources>
```

**问题**: 数据模板类型不匹配
```xml
<!-- 确保DataType正确指定 -->
<DataTemplate DataType="{x:Type local:Customer}">
    <StackPanel>
        <TextBlock Text="{Binding Name}"/>
        <TextBlock Text="{Binding Email}"/>
    </StackPanel>
</DataTemplate>
```

## 11. 最佳实践检查清单

### 11.1 代码质量检查
- [ ] 所有公共API都有XML文档注释
- [ ] 方法长度不超过20行
- [ ] 类的职责单一且明确
- [ ] 使用有意义的命名
- [ ] 避免魔法数字和字符串

### 11.2 MVVM模式检查
- [ ] View不包含业务逻辑
- [ ] ViewModel不直接引用View
- [ ] 使用Command处理用户交互
- [ ] 数据绑定使用适当的模式
- [ ] 实现属性变更通知

### 11.3 性能检查
- [ ] 大数据集使用虚拟化
- [ ] 避免在UI线程执行耗时操作
- [ ] 使用异步方法处理I/O
- [ ] 实现适当的缓存策略
- [ ] 及时释放资源

### 11.4 测试检查
- [ ] 关键业务逻辑有单元测试
- [ ] ViewModel有完整的测试覆盖
- [ ] 集成测试验证端到端功能
- [ ] UI自动化测试覆盖主要用户场景

---

*本文档将持续更新，以反映WPF开发的最新最佳实践和技术发展。*
